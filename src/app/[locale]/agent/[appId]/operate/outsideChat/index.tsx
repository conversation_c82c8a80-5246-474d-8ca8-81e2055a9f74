'use client';
import { memo, useState, useEffect } from 'react';
import { RobotOutlined } from '@ant-design/icons';
import { ProChat } from '@ant-design/pro-chat';
import { message, Typography } from 'antd';
import { useTheme } from 'antd-style';
import Cookies from 'js-cookie';
import axios, { AxiosError } from 'axios';
import { SSEResponse } from '@/utils/sseResponse';
import { VariableItem } from '@/types/agent/index';
import styles from './index.module.less';

const { Title } = Typography;

interface IProps {
  chatUrl: string;
  variables: VariableItem[];
  prompt: string;
}

const Chat: React.FC<IProps> = ({ chatUrl, variables, prompt }) => {
  const theme = useTheme();
  const [value, setValue] = useState<string>('');
  const [conversationId, setConversationId] = useState<string>('');
  const [messageId, setMessageId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [chatInputs, setChatInputs] = useState({});

  useEffect(() => {
    const obj = variables.reduce((acc, item) => {
      acc[item.variableName] = item.variableValue;
      return acc;
    }, {});
    setChatInputs(obj);
  }, [variables]);

  // 发送聊天请求
  const sendChatRequest = async (query: string) => {
    const { data } = await axios.post(
      `${chatUrl}/v1/chat-messages`,
      {
        inputs: chatInputs,
        query,
        conversation_id: conversationId,
        user: 'user',
        response_mode: 'streaming',
        parent_message_id: messageId,
        model_config: { pre_prompt: prompt },
      },
      { headers: { Authorization: Cookies.get('access_token') } },
    );
    return data.split('\n\n').filter((item: string) => item !== '');
  };

  // 创建流式响应
  const createStreamResponse = (data: string[]) => {
    const mockResponse = new SSEResponse(data);
    const reader = mockResponse.getResponse()?.body?.getReader();
    if (!reader) throw new Error('无法获取响应流');

    const decoder = new TextDecoder('utf-8');
    const encoder = new TextEncoder();

    return new ReadableStream({
      async start(controller) {
        const processChunk = async () => {
          try {
            const { done, value } = await reader.read();
            if (done) {
              controller.close();
              setLoading(false);
              return;
            }
            const chunk = decoder.decode(value, { stream: true });

            // 跳过空行和非数据行
            if (!chunk.trim() || !chunk.startsWith('data: ')) {
              processChunk();
              return;
            }

            const message = chunk.replace('data: ', '').trim();

            // 跳过心跳包和其他非JSON数据
            if (!message || message.startsWith('event:') || message === '[DONE]') {
              console.log('111111111+++++++++++++++', message)
              processChunk();
              return;
            }

            try {
              const parsed = JSON.parse(message);

              if (['agent_message', 'message', 'cache-message'].includes(parsed.event)) {
                const answer = parsed.answer || '';
                if (answer) {
                  controller.enqueue(encoder.encode(answer));
                }
              }

              if (parsed.event === 'message_end') {
                setConversationId(parsed.conversation_id);
                setMessageId(parsed.message_id);
              }
            } catch (jsonError) {
              console.log('++++++11111111133333333+++++++++++++++', message, jsonError)
              // JSON 解析失败，跳过这个数据块
              console.warn('跳过非JSON数据:', message);
            }

            processChunk();
          } catch (err) {
            console.error('读取流中的数据时发生错误', err);
            controller.error(err);
            setLoading(false);
          }
        };
        processChunk();
      },
    });
  };

  // 处理请求错误
  const handleRequestError = (e: any) => {
    setLoading(false);
    if (axios.isAxiosError(e)) {
      const error = e as AxiosError<{ msg: string }>;
      const errorMsg = error.response?.data?.msg || '未知错误，请稍后重试！';
      message.error(errorMsg);
    } else {
      message.error('系统异常，请联系管理员！');
    }
  };

  return (
    <div className={styles.chatContainer}>
      <div className={styles.chatHeader}>
        <Title level={4}>
          <RobotOutlined />
          <span>智能对话</span>
        </Title>
      </div>

      <div className={styles.chatContent} style={{ background: theme.colorBgLayout, minHeight: '90%' }}>
        <ProChat
          locale="zh-CN"
          style={{ height: '700px' }}
          userMeta={{
            avatar: '/images/logo_bg.jpeg',
          }}
          assistantMeta={{
            avatar: '/images/robot.png',
            backgroundColor: '#1677ff',
          }}
          inputAreaProps={{
            value: value,
            onChange: (e: any) => {
              setValue(e as string);
            },
            placeholder: '请输入您的问题...',
          }}
          request={async (messages: any) => {
            try {
              setLoading(true);
              const query = messages.slice(-1)[0].content;
              const data = await sendChatRequest(query);
              const stream = createStreamResponse(data);
              return new Response(stream);
            } catch (e) {
              handleRequestError(e);
            }
          }}
        />
      </div>
    </div>
  );
};
export default memo(Chat);
