'use client';
import { memo, useState, useEffect } from 'react';
import { RobotOutlined } from '@ant-design/icons';
import { ProChat } from '@ant-design/pro-chat';
import { message, Typography } from 'antd';
import { useTheme } from 'antd-style';
import Cookies from 'js-cookie';
import axios, { AxiosError } from 'axios';

import { VariableItem } from '@/types/agent/index';
import styles from './index.module.less';

const { Title } = Typography;

interface IProps {
  chatUrl: string;
  variables: VariableItem[];
  prompt: string;
}

const Chat: React.FC<IProps> = ({ chatUrl, variables, prompt }) => {
  const theme = useTheme();
  const [value, setValue] = useState<string>('');
  const [conversationId, setConversationId] = useState<string>('');
  const [messageId, setMessageId] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [chatInputs, setChatInputs] = useState({});

  // 控制是否启用打字机效果
  const enableTypewriter = false; // 设置为 false 可以立即显示所有内容

  useEffect(() => {
    const obj = variables.reduce((acc, item) => {
      acc[item.variableName] = item.variableValue;
      return acc;
    }, {});
    setChatInputs(obj);
  }, [variables]);

  // 发送聊天请求
  const sendChatRequest = async (query: string) => {
    const { data } = await axios.post(
      `${chatUrl}/v1/chat-messages`,
      {
        inputs: chatInputs,
        query,
        conversation_id: conversationId,
        user: 'user',
        response_mode: 'streaming',
        parent_message_id: messageId,
        model_config: { pre_prompt: prompt },
      },
      { headers: { Authorization: Cookies.get('access_token') } },
    );
    return data.split('\n\n').filter((item: string) => item !== '');
  };

  // 创建流式响应 - 即时显示版本
  const createStreamResponse = (data: string[]) => {
    const encoder = new TextEncoder();
    let fullText = '';

    return new ReadableStream({
      async start(controller) {
        try {
          // 预处理所有数据，提取完整文本
          for (const chunk of data) {
            try {
              const message = chunk.replace('data: ', '');
              const parsed = JSON.parse(message);

              if (['agent_message', 'message', 'cache-message'].includes(parsed.event)) {
                fullText += parsed.answer || '';
              }
              if (parsed.event === 'message_end') {
                setConversationId(parsed.conversation_id);
                setMessageId(parsed.message_id);
              }
            } catch (err) {
              console.error('解析数据块时发生错误', err);
            }
          }

          // 根据配置决定显示方式
          if (!enableTypewriter) {
            // 立即显示所有内容
            controller.enqueue(encoder.encode(fullText));
            controller.close();
            setLoading(false);
          } else {
            // 打字机效果
            let currentPos = 0;
            const chunkSize = 5; // 每次显示5个字符
            const delay = 20; // 20ms延迟，快速打字机效果

            const typeWriter = () => {
              if (currentPos >= fullText.length) {
                controller.close();
                setLoading(false);
                return;
              }

              const nextChunk = fullText.slice(currentPos, currentPos + chunkSize);
              controller.enqueue(encoder.encode(nextChunk));
              currentPos += chunkSize;

              setTimeout(typeWriter, delay);
            };

            typeWriter();
          }
        } catch (err) {
          console.error('处理响应数据时发生错误', err);
          controller.error(err);
          setLoading(false);
        }
      },
    });
  };

  // 处理请求错误
  const handleRequestError = (e: any) => {
    setLoading(false);
    if (axios.isAxiosError(e)) {
      const error = e as AxiosError<{ msg: string }>;
      const errorMsg = error.response?.data?.msg || '未知错误，请稍后重试！';
      message.error(errorMsg);
    } else {
      message.error('系统异常，请联系管理员！');
    }
  };

  return (
    <div className={styles.chatContainer}>
      <div className={styles.chatHeader}>
        <Title level={4}>
          <RobotOutlined />
          <span>智能对话</span>
        </Title>
      </div>

      <div className={styles.chatContent} style={{ background: theme.colorBgLayout, minHeight: '90%' }}>
        <ProChat
          locale="zh-CN"
          style={{ height: '700px' }}
          userMeta={{
            avatar: '/images/logo_bg.jpeg',
          }}
          assistantMeta={{
            avatar: '/images/robot.png',
            backgroundColor: '#1677ff',
          }}
          inputAreaProps={{
            value: value,
            onChange: (e: any) => {
              setValue(e as string);
            },
            placeholder: '请输入您的问题...',
          }}
          request={async (messages: any) => {
            try {
              setLoading(true);
              const query = messages.slice(-1)[0].content;
              const data = await sendChatRequest(query);
              const stream = createStreamResponse(data);
              return new Response(stream);
            } catch (e) {
              handleRequestError(e);
            }
          }}
        />
      </div>
    </div>
  );
};
export default memo(Chat);
