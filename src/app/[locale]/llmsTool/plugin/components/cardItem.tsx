'use client';
import React from 'react';
import { Card, Avatar, Typography, Button } from 'antd';
import { EditOutlined, DeleteOutlined, SettingOutlined } from '@ant-design/icons';
import { ToolItem } from '@/types/tool';
import styles from './cardItem.module.less';

const { Text, Paragraph } = Typography;

interface CardItemProps {
  item: ToolItem;
  onEdit?: (item: ToolItem) => void;
  onDelete?: (item: ToolItem) => void;
  onConfig?: (item: ToolItem) => void;
}

const CardItem: React.FC<CardItemProps> = ({ item, onEdit, onDelete, onConfig }) => {
  const handleEdit = () => {
    onEdit?.(item);
  };

  const handleDelete = () => {
    onDelete?.(item);
  };

  const handleConfig = () => {
    onConfig?.(item);
  };

  return (
    <Card
      hoverable
      className={styles['custom-card']}
      actions={[
        <Button key="config" type="text" icon={<SettingOutlined />} onClick={handleConfig} size="small">
          工具
        </Button>,
        <Button key="edit" type="text" icon={<EditOutlined />} onClick={handleEdit} size="small">
          编辑
        </Button>,
        <Button key="delete" type="text" icon={<DeleteOutlined />} onClick={handleDelete} size="small" danger>
          删除
        </Button>,
      ]}
    >
      <div className={styles['card-body']}>
        <div className={styles['card-avatar']}>
          <Avatar
            src={item.icon}
            icon={!item.icon && <SettingOutlined />}
            size={40}
            style={{
              backgroundColor: !item.icon ? '#f0f0f0' : undefined,
              marginRight: '12px',
              flexShrink: 0,
            }}
          />
        </div>
        <div className={styles['card-text-content']}>
          <Text strong className={styles['card-header-title']} ellipsis>
            {item.name}
          </Text>
          <Paragraph ellipsis={{ rows: 2, expandable: false }} className={styles['card-description-text']}>
            {item.description || '暂无描述'}
          </Paragraph>
        </div>
      </div>
    </Card>
  );
};

export default CardItem;
