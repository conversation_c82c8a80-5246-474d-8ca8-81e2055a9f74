// 卡片主体样式
.card-body {
    display: flex;
    padding: 0;

    .card-text-content {
        .card-header-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-top: 2px;
            margin-bottom: 10px;
        }

        .card-description-text {
            margin: 0;
            color: #8c8c8c;
            font-size: 14px;
            line-height: 1.6;
            height: auto;
            min-height: 44px;
        }
    }
}

.custom-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

    // 全局卡片样式覆盖
    :global {
        .ant-card {
            border-radius: 12px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;

            &:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                border-color: #d9d9d9;
            }

            .ant-card-body {
                padding: 20px;
            }

            // 底部操作按钮样式
            .ant-card-actions {
                border-top: 1px solid #f0f0f0;
                background: #fafafa;

                >li {
                    margin: 8px 0;

                    .ant-btn {
                        color: #8c8c8c;
                        font-size: 13px;
                        height: 28px;
                        padding: 4px 8px;
                        border-radius: 6px;
                        transition: all 0.2s ease;

                        &:hover {
                            color: #1677ff;
                            background-color: #f0f7ff;
                        }

                        .anticon {
                            font-size: 14px;
                            margin-right: 4px;
                        }
                    }

                    &:not(:last-child) {
                        border-right: 1px solid #f0f0f0;
                    }
                }
            }
        }

    }
}