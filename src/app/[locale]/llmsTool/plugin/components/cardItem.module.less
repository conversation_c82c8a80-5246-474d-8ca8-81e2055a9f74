// 卡片主体样式
.card-body {
    display: flex;
    padding: 0;

    .card-text-content {
        .card-header-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-top: 2px;
            margin-bottom: 10px;
        }

        .card-description-text {
            margin: 0;
            color: #8c8c8c;
            font-size: 14px;
            line-height: 1.6;
            height: auto;
            min-height: 44px;
        }
    }
}

.custom-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.custom-card-actions {
    border-top: 1px solid #f0f0f0;
    background: linear-gradient(180deg, #deedfc 0%, #ffffff 100%) !important;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.06) 50%, transparent 100%);
    }

    >li {
        margin: 8px 0;

        .ant-btn {
            color: rgb(200, 223, 255);
            font-size: 13px;
            height: 28px;
            padding: 4px 8px;
            border-radius: 6px;
            transition: all 0.2s ease;


            .anticon {
                font-size: 14px;
                margin-right: 4px;
            }
        }

        &:not(:last-child) {
            border-right: 1px solid #f0f0f0;
        }
    }
}